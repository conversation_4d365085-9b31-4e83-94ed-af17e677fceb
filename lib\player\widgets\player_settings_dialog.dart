import 'package:flutter/material.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import '../models/player_track.dart';

/// Enhanced settings dialog with tabbed interface for video player
class PlayerSettingsDialog extends StatefulWidget {
  final CustomPlayerController controller;

  const PlayerSettingsDialog({
    super.key,
    required this.controller,
  });

  @override
  State<PlayerSettingsDialog> createState() => _PlayerSettingsDialogState();
}

class _PlayerSettingsDialogState extends State<PlayerSettingsDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final TextEditingController _subtitleUrlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _subtitleUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: PlayerTheme.themeData,
      child: AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 200),
        child: Dialog(
          backgroundColor: PlayerTheme.surfaceColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(PlayerTheme.borderRadius),
          ),
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.7,
              constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
              child: Column(
                children: [
                  // Header with title and close button
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: PlayerTheme.textDisabled,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Text(
                          'Player Settings',
                          style: PlayerTheme.titleTextStyle,
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.close_rounded),
                          color: PlayerTheme.buttonColor,
                          onPressed: () {
                            _animationController.reverse().then((_) {
                              Navigator.of(context).pop();
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  // Tab bar
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: PlayerTheme.textDisabled,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicatorColor: PlayerTheme.primaryColor,
                      labelColor: PlayerTheme.primaryColor,
                      unselectedLabelColor: PlayerTheme.textSecondary,
                      tabs: const [
                        Tab(
                          icon: Icon(PlayerTheme.qualityIcon, size: 20),
                          text: 'Quality',
                        ),
                        Tab(
                          icon: Icon(PlayerTheme.audioTrackIcon, size: 20),
                          text: 'Audio',
                        ),
                        Tab(
                          icon: Icon(PlayerTheme.subtitlesIcon, size: 20),
                          text: 'Subtitles',
                        ),
                        Tab(
                          icon: Icon(PlayerTheme.speedIcon, size: 20),
                          text: 'Playback',
                        ),
                      ],
                    ),
                  ),
                  // Tab content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildQualityTab(),
                        _buildAudioTab(),
                        _buildSubtitlesTab(),
                        _buildPlaybackTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ), // ScaleTransition
        ), // Dialog
      ), // AnimatedOpacity
    );
  }

  Widget _buildQualityTab() {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final videoTracks = widget.controller.playerTracks.videoTracks;

        if (videoTracks.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  PlayerTheme.qualityIcon,
                  size: 48,
                  color: PlayerTheme.textDisabled,
                ),
                SizedBox(height: 16),
                Text(
                  'No quality options available',
                  style: PlayerTheme.subtitleTextStyle,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: videoTracks.length,
          itemBuilder: (context, index) {
            final track = videoTracks[index];
            String? subtitle;

            if (track.id == 'auto') {
              subtitle = track.resolution;
            } else if (track.resolution != null) {
              subtitle = track.resolution;
              if (track.bitrate != null && track.bitrate! > 0) {
                final kbps = (track.bitrate! / 1000).toStringAsFixed(0);
                subtitle = '$subtitle • ${kbps}kbps';
              }
            } else if (track.bitrate != null && track.bitrate! > 0) {
              final kbps = (track.bitrate! / 1000).toStringAsFixed(0);
              subtitle = '${kbps}kbps';
            }

            return _buildTrackTile(
              title: track.label,
              subtitle: subtitle,
              isSelected: track.isSelected,
              onTap: () => widget.controller.selectVideoTrack(track),
              isAuto: track.id == 'auto',
            );
          },
        );
      },
    );
  }

  Widget _buildAudioTab() {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final audioTracks = widget.controller.playerTracks.audioTracks;

        if (audioTracks.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  PlayerTheme.audioTrackIcon,
                  size: 48,
                  color: PlayerTheme.textDisabled,
                ),
                SizedBox(height: 16),
                Text(
                  'No audio tracks available',
                  style: PlayerTheme.subtitleTextStyle,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: audioTracks.length,
          itemBuilder: (context, index) {
            final track = audioTracks[index];
            String? subtitle;

            if (track.id == 'auto') {
              subtitle = track.language;
            } else if (track.language != null) {
              subtitle = track.language;
              if (track.codec != null) {
                subtitle = '$subtitle • ${track.codec}';
              }
            } else if (track.codec != null) {
              subtitle = track.codec;
            }

            return _buildTrackTile(
              title: track.label,
              subtitle: subtitle,
              isSelected: track.isSelected,
              onTap: () => widget.controller.selectAudioTrack(track),
              isAuto: track.id == 'auto',
            );
          },
        );
      },
    );
  }

  Widget _buildSubtitlesTab() {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final subtitleTracks = widget.controller.playerTracks.subtitleTracks;

        return Column(
          children: [
            // Add subtitle options
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: PlayerTheme.textDisabled,
                    width: 0.5,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Load External Subtitles',
                    style: PlayerTheme.subtitleTextStyle,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _subtitleUrlController,
                          style:
                              const TextStyle(color: PlayerTheme.textPrimary),
                          decoration: const InputDecoration(
                            hintText: 'Enter subtitle URL',
                            hintStyle:
                                TextStyle(color: PlayerTheme.textDisabled),
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(PlayerTheme.linkIcon),
                        color: PlayerTheme.primaryColor,
                        onPressed: _loadSubtitleFromUrl,
                        tooltip: 'Load from URL',
                      ),
                      IconButton(
                        icon: const Icon(PlayerTheme.fileUploadIcon),
                        color: PlayerTheme.primaryColor,
                        onPressed: _loadSubtitleFromFile,
                        tooltip: 'Load from file',
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Subtitle tracks list
            Expanded(
              child: subtitleTracks.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            PlayerTheme.subtitlesOffIcon,
                            size: 48,
                            color: PlayerTheme.textDisabled,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No subtitles available',
                            style: PlayerTheme.subtitleTextStyle,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: subtitleTracks.length,
                      itemBuilder: (context, index) {
                        final track = subtitleTracks[index];
                        return _buildTrackTile(
                          title: track.label,
                          subtitle: track.language ?? track.type.displayName,
                          isSelected: track.isSelected,
                          onTap: () =>
                              widget.controller.selectSubtitleTrack(track),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPlaybackTab() {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Playback speed
            _buildPlaybackSpeedSection(),
            const SizedBox(height: 24),
            // Volume
            _buildVolumeSection(),
          ],
        );
      },
    );
  }

  Widget _buildTrackTile({
    required String title,
    String? subtitle,
    required bool isSelected,
    required VoidCallback onTap,
    bool isAuto = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color:
            isSelected ? PlayerTheme.primaryColor.withValues(alpha: 0.1) : null,
        border: isAuto && isSelected
            ? Border.all(color: PlayerTheme.primaryColor.withValues(alpha: 0.3))
            : null,
      ),
      child: ListTile(
        leading: isAuto
            ? Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: PlayerTheme.primaryColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.auto_awesome_rounded,
                  color: PlayerTheme.primaryColor,
                  size: 18,
                ),
              )
            : null,
        title: Text(
          title,
          style: PlayerTheme.titleTextStyle.copyWith(
            fontSize: 16,
            fontWeight: isAuto ? FontWeight.w600 : FontWeight.normal,
            color: isAuto ? PlayerTheme.primaryColor : PlayerTheme.textPrimary,
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: PlayerTheme.subtitleTextStyle.copyWith(
                  color: isAuto
                      ? PlayerTheme.primaryColor.withValues(alpha: 0.8)
                      : PlayerTheme.textSecondary,
                ),
              )
            : null,
        trailing: isSelected
            ? Icon(
                Icons.check_circle_rounded,
                color: isAuto
                    ? PlayerTheme.primaryColor
                    : PlayerTheme.primaryColor,
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildPlaybackSpeedSection() {
    final speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Playback Speed',
          style: PlayerTheme.titleTextStyle,
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: speeds.map((speed) {
            final isSelected = widget.controller.playbackSpeed == speed;
            return ChoiceChip(
              label: Text('${speed}x'),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  widget.controller.setPlaybackSpeed(speed);
                }
              },
              selectedColor: PlayerTheme.primaryColor,
              backgroundColor: PlayerTheme.controlBackground,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : PlayerTheme.textPrimary,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildVolumeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Volume', style: PlayerTheme.titleTextStyle),
        const SizedBox(height: 12),
        Row(
          children: [
            // Mute/unmute button
            IconButton(
              icon: Icon(
                widget.controller.isMuted
                    ? PlayerTheme.volumeOffIcon
                    : PlayerTheme.volumeUpIcon,
              ),
              color: PlayerTheme.buttonColor,
              onPressed: () => widget.controller.toggleMute(),
            ),
            // Volume slider
            Expanded(
              child: Slider(
                value: widget.controller.volume,
                onChanged: (value) {
                  widget.controller.setVolume(value);
                  if (widget.controller.isMuted && value > 0) {
                    widget.controller.toggleMute();
                  }
                },
                divisions: 20,
              ),
            ),
            // Volume percentage
            SizedBox(
              width: 50,
              child: Text(
                '${(widget.controller.volume * 100).round()}%',
                style: PlayerTheme.subtitleTextStyle,
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _loadSubtitleFromUrl() {
    final url = _subtitleUrlController.text.trim();
    if (url.isNotEmpty) {
      widget.controller.loadSubtitleFromUrl(url);
      _subtitleUrlController.clear();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Loading subtitle...'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _loadSubtitleFromFile() {
    widget.controller.loadSubtitleFromFile();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Loading subtitle file...'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
